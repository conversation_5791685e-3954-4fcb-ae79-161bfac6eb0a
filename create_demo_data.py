import os
import django
import random
from datetime import datetime, timedelta
import pytz

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from django.utils import timezone
from django.db import transaction

# Import models
from user.models import User, MetaData
from members.models import Package, Member
from product.models import Category, Product, Supplier, Purchase, PurchaseItem, Sale, SaleItem
from paypervisit.models import PayPerVisit, PayPerVisitSettings
from payment.models import Payment, PaymentTemplate
from payroll.models import SalaryPayment
from finance.models import Transaction as FinanceTransaction
from billmanagement.models import Bill
from settings.models import Settings

# Utility functions
def generate_phone():
    """Generate a random Cambodian phone number with proper format"""
    # Common Cambodian mobile prefixes
    prefixes = ['010', '012', '015', '016', '017', '069', '070', '077', '078', '085', '086', '087', '092', '093', '095', '096', '097', '098']
    return f"{random.choice(prefixes)} {random.randint(100, 999)} {random.randint(100, 999)}"

def generate_transaction_id(prefix):
    """Generate a unique transaction ID with the given prefix"""
    return f"{prefix}-{random.randint(10000, 99999)}"

def random_date(start_date, end_date):
    """Generate a random date between start_date and end_date"""
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    return start_date + timedelta(days=random_days)

def get_cambodian_names():
    """Get comprehensive lists of authentic Cambodian names"""
    male_first_names = [
        'Dara', 'Sokha', 'Veasna', 'Kosal', 'Makara', 'Chamroeun', 'Vannak', 'Rithy', 'Pisach', 'Thida',
        'Sophea', 'Ratana', 'Pich', 'Sokhom', 'Kunthea', 'Virak', 'Samnang', 'Bunroeun', 'Chenda', 'Keo',
        'Mony', 'Ratha', 'Sovannak', 'Thearith', 'Vicheka', 'Borey', 'Chanthy', 'Darith', 'Heng', 'Kimheng'
    ]

    female_first_names = [
        'Bopha', 'Srey', 'Sopheap', 'Chenda', 'Phalla', 'Sothea', 'Chantha', 'Thida', 'Sreypov', 'Channary',
        'Devi', 'Kanha', 'Leap', 'Mealea', 'Neary', 'Pheakdey', 'Rachana', 'Socheat', 'Tevy', 'Vanna',
        'Bopha', 'Chanthou', 'Davy', 'Gech', 'Heng', 'Jorani', 'Kaliyan', 'Linda', 'Mony', 'Nita'
    ]

    last_names = [
        'Sok', 'Chhay', 'Meas', 'Prak', 'Tep', 'Yong', 'Khun', 'Keo', 'Chea', 'Pov',
        'Heng', 'Seng', 'Nhem', 'Vann', 'Leng', 'Mao', 'Ung', 'Yin', 'Lim', 'Chum',
        'Ros', 'San', 'Tan', 'Vong', 'Watt', 'Yim', 'Chhorn', 'Duch', 'Ean', 'Fong'
    ]

    return male_first_names, female_first_names, last_names

def generate_cambodian_address():
    """Generate a realistic Cambodian address"""
    districts = [
        'Chamkar Mon', 'Doun Penh', 'Prampir Meakkakra', 'Tuol Kouk', 'Dangkao', 'Mean Chey',
        'Russey Keo', 'Sen Sok', 'Pou Senchey', 'Chroy Changvar', 'Prek Pnov', 'Chbar Ampov'
    ]

    street_types = ['Street', 'St.', 'Road', 'Blvd']

    house_num = random.randint(1, 999)
    street_num = random.randint(1, 999)
    district = random.choice(districts)
    street_type = random.choice(street_types)

    return f"#{house_num}, {street_type} {street_num}, {district}, Phnom Penh"

# Create users with different roles
def create_users():
    print("Creating users with different roles...")

    # Create other users with different roles using authentic Cambodian names
    roles = [
        {
            'username': 'coach1',
            'first_name': 'Sokha',
            'last_name': 'Meas',
            'role': 'coach',
            'salary': 900000,
            'is_manager': False,
            'is_employee': True,
            'gender': 'male'
        },
        {
            'username': 'cashier1',
            'first_name': 'Bopha',
            'last_name': 'Chhay',
            'role': 'cashier',
            'salary': 800000,
            'is_manager': False,
            'is_employee': True,
            'gender': 'female'
        },
        {
            'username': 'cashier2',
            'first_name': 'Sreypov',
            'last_name': 'Prak',
            'role': 'cashier',
            'salary': 800000,
            'is_manager': False,
            'is_employee': True,
            'gender': 'female'
        },
        {
            'username': 'cleaner1',
            'first_name': 'Srey',
            'last_name': 'Tep',
            'role': 'cleaner',
            'salary': 600000,
            'is_manager': False,
            'is_employee': True,
            'gender': 'female'
        },
        {
            'username': 'security1',
            'first_name': 'Rithy',
            'last_name': 'Khun',
            'role': 'security',
            'salary': 700000,
            'is_manager': False,
            'is_employee': True,
            'gender': 'male'
        }
    ]

    for i, role_data in enumerate(roles, 1):  # Start from 1 since admin is LFC-000
        if not User.objects.filter(username=role_data['username']).exists():
            full_name = f"{role_data['first_name']} {role_data['last_name']}"
            user = User.objects.create_user(
                username=role_data['username'],
                password='password123',
                email=f"{role_data['username']}@legendfitness.com",
                name=full_name,
                phone=generate_phone(),
                role=role_data['role'],
                is_manager=role_data['is_manager'],
                is_employee=role_data['is_employee'],
                salary=role_data['salary'],
                join_date=timezone.now().date() - timedelta(days=random.randint(30, 300)),
                gender=role_data['gender'],
                emp_id=f"LFC-{i:04d}",
                address=generate_cambodian_address(),
                dob=datetime(random.randint(1985, 2000), random.randint(1, 12), random.randint(1, 28)).date()
            )
            print(f"Created {role_data['role']} user: {user.username} ({full_name})")

    print(f"Created {len(roles)} users")

# Create packages
def create_packages():
    print("Creating membership packages...")
    packages = [
        {
            'package_id': 'PKG-001',
            'name': 'Basic',
            'duration': 1,
            'price_khr': 120000,
            'price_usd': 30,
            'access_type': 'peak_only',
            'description': 'Basic package with access during peak hours only.'
        },
        {
            'package_id': 'PKG-002',
            'name': 'Premium',
            'duration': 3,
            'price_khr': 300000,
            'price_usd': 75,
            'access_type': 'all_hours',
            'description': 'Premium package with all-hours access and 1 free PT session.'
        },
        {
            'package_id': 'PKG-003',
            'name': 'VIP',
            'duration': 6,
            'price_khr': 550000,
            'price_usd': 137.5,
            'access_type': 'all_hours',
            'description': 'VIP package with all-hours access, locker, and 3 free PT sessions.'
        },
        {
            'package_id': 'PKG-004',
            'name': 'Super VIP',
            'duration': 12,
            'price_khr': 1000000,
            'price_usd': 250,
            'access_type': 'all_hours',
            'description': 'Super VIP package with all-hours access, locker, and 10 free PT sessions.'
        }
    ]

    for package_data in packages:
        Package.objects.get_or_create(
            package_id=package_data['package_id'],
            defaults=package_data
        )
    print(f"Created {len(packages)} packages")

# Create members
def create_members():
    print("Creating sample members...")
    packages = Package.objects.all()
    if not packages:
        print("No packages found. Please create packages first.")
        return

    male_names, female_names, last_names = get_cambodian_names()

    # Create 8 members with varied membership statuses
    for i in range(1, 9):
        # Determine gender and select appropriate name
        gender = random.choice(['male', 'female'])
        if gender == 'male':
            first_name = random.choice(male_names)
        else:
            first_name = random.choice(female_names)

        last_name = random.choice(last_names)
        name = f"{first_name} {last_name}"

        # Generate member ID
        member_id = f"M-{1000 + i}"

        # Assign a random package with weighted distribution (more basic packages)
        package_weights = [0.4, 0.3, 0.2, 0.1]  # Basic, Premium, VIP, Super VIP
        package = random.choices(list(packages), weights=package_weights)[0]

        # Calculate start and end dates with varied scenarios
        if i <= 3:  # Active members
            start_date = timezone.now().date() - timedelta(days=random.randint(0, 30))
            payment_status = 'paid'
        elif i <= 6:  # Some near expiration
            start_date = timezone.now().date() - timedelta(days=random.randint(60, 90))
            payment_status = random.choice(['paid', 'pending'])
        else:  # Some expired or new
            start_date = timezone.now().date() - timedelta(days=random.randint(0, 120))
            payment_status = random.choice(['paid', 'pending', 'overdue'])

        end_date = start_date + timedelta(days=package.duration * 30)

        # Generate realistic discount (10% get discounts)
        discount = random.choice([0, 0, 0, 0, 0, 0, 0, 0, 10000, 20000])

        # Create the member if it doesn't exist
        if not Member.objects.filter(member_id=member_id).exists():
            member = Member.objects.create(
                member_id=member_id,
                name=name,
                gender=gender,
                dob=datetime(random.randint(1985, 2005), random.randint(1, 12), random.randint(1, 28)).date(),
                contact=generate_phone(),
                telegram=f"@{first_name.lower()}{random.randint(100, 999)}",
                address=generate_cambodian_address(),
                package=package,
                start_date=start_date,
                end_date=end_date,
                payment_status=payment_status,
                discount=discount,
                due_payment=0 if payment_status == 'paid' else (package.price_khr - discount)
            )

            # Print status for tracking
            status = "Active"
            if member.end_date < timezone.now().date():
                status = "Expired"
            elif member.is_expiring_soon:
                status = "Expiring Soon"

            print(f"Created member: {name} with {package.name} package ({status})")

    print(f"Created 8 members")

# Create product categories and products
def create_products_and_suppliers():
    print("Creating product categories, suppliers, and products...")

    # Create categories
    categories = [
        {'name': 'Beverages', 'description': 'Drinks and refreshments'},
        {'name': 'Snacks', 'description': 'Healthy snacks and energy bars'},
        {'name': 'Supplements', 'description': 'Protein powders and supplements'},
        {'name': 'Merchandise', 'description': 'Gym branded merchandise'}
    ]

    for category_data in categories:
        Category.objects.get_or_create(
            name=category_data['name'],
            defaults=category_data
        )

    # Get the categories
    beverage_category = Category.objects.get(name='Beverages')
    snack_category = Category.objects.get(name='Snacks')
    supplement_category = Category.objects.get(name='Supplements')
    merchandise_category = Category.objects.get(name='Merchandise')

    # Create suppliers
    suppliers = [
        {
            'name': 'ABC Beverages',
            'phone': generate_phone(),
            'telegram': '@abcbeverages',
            'address': '#123, St. 271, Phnom Penh',
            'note': 'Main supplier for all beverages'
        },
        {
            'name': 'HealthySnacks Co.',
            'phone': generate_phone(),
            'telegram': '@healthysnacks',
            'address': '#45, St. 310, Phnom Penh',
            'note': 'Supplies protein bars and healthy snacks'
        },
        {
            'name': 'Supplement World',
            'phone': generate_phone(),
            'telegram': '@supplementworld',
            'address': '#78, St. 163, Phnom Penh',
            'note': 'Premium supplements and protein powders'
        },
        {
            'name': 'Fitness Gear',
            'phone': generate_phone(),
            'telegram': '@fitnessgear',
            'address': '#92, St. 214, Phnom Penh',
            'note': 'Gym merchandise and branded items'
        }
    ]

    for supplier_data in suppliers:
        Supplier.objects.get_or_create(
            name=supplier_data['name'],
            defaults=supplier_data
        )

    # Get the suppliers
    beverage_supplier = Supplier.objects.get(name='ABC Beverages')
    snack_supplier = Supplier.objects.get(name='HealthySnacks Co.')
    supplement_supplier = Supplier.objects.get(name='Supplement World')
    merchandise_supplier = Supplier.objects.get(name='Fitness Gear')

    # Create products
    products = [
        {
            'name': 'Mineral Water',
            'sku': 'BEV-001',
            'category': beverage_category,
            'cost_price': 1500,
            'retail_price': 2000,
            'quantity': 100,
            'description': '500ml bottled water',
            'box_quantity': 24,
            'box_cost': 36000
        },
        {
            'name': 'Sports Drink',
            'sku': 'BEV-002',
            'category': beverage_category,
            'cost_price': 4000,
            'retail_price': 5000,
            'quantity': 50,
            'description': 'Electrolyte-enhanced sports drink',
            'box_quantity': 12,
            'box_cost': 48000
        },
        {
            'name': 'Protein Bar',
            'sku': 'SNK-001',
            'category': snack_category,
            'cost_price': 6000,
            'retail_price': 8000,
            'quantity': 30,
            'description': '20g protein bar',
            'box_quantity': 12,
            'box_cost': 72000
        },
        {
            'name': 'Energy Bar',
            'sku': 'SNK-002',
            'category': snack_category,
            'cost_price': 5000,
            'retail_price': 7000,
            'quantity': 40,
            'description': 'High-energy snack bar',
            'box_quantity': 24,
            'box_cost': 120000
        },
        {
            'name': 'Whey Protein',
            'sku': 'SUP-001',
            'category': supplement_category,
            'cost_price': 120000,
            'retail_price': 150000,
            'quantity': 15,
            'description': '1kg whey protein powder',
            'box_quantity': 6,
            'box_cost': 720000
        },
        {
            'name': 'BCAA Supplement',
            'sku': 'SUP-002',
            'category': supplement_category,
            'cost_price': 80000,
            'retail_price': 100000,
            'quantity': 10,
            'description': 'Branched-chain amino acids supplement',
            'box_quantity': 12,
            'box_cost': 960000
        },
        {
            'name': 'Gym T-Shirt',
            'sku': 'MER-001',
            'category': merchandise_category,
            'cost_price': 30000,
            'retail_price': 40000,
            'quantity': 20,
            'description': 'Legend Fitness branded t-shirt',
            'box_quantity': 10,
            'box_cost': 300000
        },
        {
            'name': 'Gym Towel',
            'sku': 'MER-002',
            'category': merchandise_category,
            'cost_price': 18000,
            'retail_price': 25000,
            'quantity': 25,
            'description': 'Legend Fitness branded towel',
            'box_quantity': 20,
            'box_cost': 360000
        },
        {
            'name': 'Creatine Monohydrate',
            'sku': 'SUP-003',
            'category': supplement_category,
            'cost_price': 60000,
            'retail_price': 80000,
            'quantity': 12,
            'description': '300g creatine monohydrate powder',
            'box_quantity': 8,
            'box_cost': 480000
        },
        {
            'name': 'Gym Water Bottle',
            'sku': 'MER-003',
            'category': merchandise_category,
            'cost_price': 25000,
            'retail_price': 35000,
            'quantity': 30,
            'description': 'Legend Fitness branded 750ml water bottle',
            'box_quantity': 12,
            'box_cost': 300000
        },
        {
            'name': 'Pre-Workout',
            'sku': 'SUP-004',
            'category': supplement_category,
            'cost_price': 70000,
            'retail_price': 95000,
            'quantity': 8,
            'description': '250g pre-workout energy supplement',
            'box_quantity': 6,
            'box_cost': 420000
        },
        {
            'name': 'Gym Gloves',
            'sku': 'MER-004',
            'category': merchandise_category,
            'cost_price': 35000,
            'retail_price': 50000,
            'quantity': 15,
            'description': 'Training gloves for weightlifting',
            'box_quantity': 8,
            'box_cost': 280000
        }
    ]

    for product_data in products:
        Product.objects.get_or_create(
            name=product_data['name'],
            defaults=product_data
        )

    print(f"Created {len(categories)} categories, {len(suppliers)} suppliers, and {len(products)} products")

    return {
        'beverage_supplier': beverage_supplier,
        'snack_supplier': snack_supplier,
        'supplement_supplier': supplement_supplier,
        'merchandise_supplier': merchandise_supplier
    }

# Create purchases and sales
def create_purchases_and_sales():
    print("Creating purchases and sales...")

    # Get all products
    products = Product.objects.all()
    if not products:
        print("No products found. Please create products first.")
        return

    # Get suppliers
    suppliers = Supplier.objects.all()
    if not suppliers:
        print("No suppliers found. Please create suppliers first.")
        return

    # Get cashiers
    cashiers = User.objects.filter(role='cashier')
    if not cashiers:
        print("No cashiers found. Please create users first.")
        return

    # Create purchases (16 purchases in the last 30 days)
    for i in range(1, 17):
        # Generate purchase date
        purchase_date = timezone.now() - timedelta(days=random.randint(1, 30))

        # Select a random supplier based on product category
        product = random.choice(products)
        supplier = None
        if 'BEV' in product.sku:
            supplier = Supplier.objects.get(name='ABC Beverages')
        elif 'SNK' in product.sku:
            supplier = Supplier.objects.get(name='HealthySnacks Co.')
        elif 'SUP' in product.sku:
            supplier = Supplier.objects.get(name='Supplement World')
        elif 'MER' in product.sku:
            supplier = Supplier.objects.get(name='Fitness Gear')
        else:
            supplier = random.choice(suppliers)

        # Generate unique transaction ID
        trx_id = generate_transaction_id("PO")

        # Create the purchase
        purchase = Purchase.objects.create(
            trxId=trx_id,
            supplier=supplier,
            date=purchase_date,
            total_amount=0,  # Will be updated after adding items
            payment_method=random.choice(['cash', 'bank', 'other']),
            notes=f"Stock replenishment for {product.category.name}",
            created_by=random.choice(User.objects.filter(role__in=['admin', 'employee']))
        )

        # Add 1-3 items to the purchase
        total_amount = 0
        for j in range(random.randint(1, 3)):
            # Select a product from the same category as the first product
            if j == 0:
                selected_product = product
            else:
                category_products = Product.objects.filter(category=product.category)
                if category_products.count() > 1:
                    selected_product = random.choice(category_products.exclude(id=product.id))
                else:
                    selected_product = product

            # Decide if it's a box purchase
            is_box_purchase = random.choice([True, False])

            if is_box_purchase:
                # Box purchase
                box_quantity = selected_product.box_quantity
                original_quantity = random.randint(1, 5)  # Number of boxes
                quantity = original_quantity * box_quantity  # Total units
                cost_price = selected_product.cost_price
                box_cost = selected_product.box_cost
                item_total = original_quantity * box_cost
            else:
                # Unit purchase
                original_quantity = random.randint(5, 20)  # Number of units
                quantity = original_quantity
                cost_price = selected_product.cost_price
                box_cost = None
                item_total = quantity * cost_price

            # Add the item to the purchase
            PurchaseItem.objects.create(
                purchase=purchase,
                product=selected_product,
                quantity=quantity,
                cost_price=cost_price,
                is_box_purchase=is_box_purchase,
                box_quantity=selected_product.box_quantity,
                original_quantity=original_quantity,
                stored_box_cost=box_cost if is_box_purchase else 0
            )

            # Update product quantity
            selected_product.quantity += quantity
            selected_product.save()

            # Add to total amount
            total_amount += item_total

        # Update purchase total
        purchase.total_amount = total_amount
        purchase.save()

        print(f"Created purchase {purchase.trxId} with {purchase.items.count()} items for {purchase.total_amount}៛")

    # Create sales (4 sales in the last 14 days)
    for i in range(1, 5):
        # Generate sale date
        sale_date = timezone.now() - timedelta(days=random.randint(0, 14))

        # Select a random cashier
        cashier = random.choice(cashiers)

        # Create the sale
        sale = Sale.objects.create(
            trxId=generate_transaction_id("SALE"),
            date=sale_date,
            total_amount=0,  # Will be updated after adding items
            payment_method=random.choice(['cash', 'card']),
            sold_by=cashier,
            notes="Regular sale"
        )

        # Add 1-5 items to the sale
        total_amount = 0
        for j in range(random.randint(1, 5)):
            # Select a random product with available stock
            in_stock_products = Product.objects.filter(quantity__gt=0)
            if not in_stock_products:
                break

            selected_product = random.choice(in_stock_products)

            # Determine quantity to sell (not more than available)
            max_quantity = min(selected_product.quantity, 5)
            if max_quantity < 1:
                continue

            quantity = random.randint(1, max_quantity)

            # Add the item to the sale
            SaleItem.objects.create(
                sale=sale,
                product=selected_product,
                quantity=quantity,
                price=selected_product.retail_price,
                is_box_equivalent=False,
                box_quantity=selected_product.box_quantity
            )

            # Update product quantity
            selected_product.quantity -= quantity
            selected_product.save()

            # Add to total amount
            item_total = quantity * selected_product.retail_price
            total_amount += item_total

        # Update sale total
        sale.total_amount = total_amount
        sale.save()

        # Update funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds += total_amount
            meta.save()

        print(f"Created sale {sale.trxId} with {sale.items.count()} items for {sale.total_amount}៛")

    print(f"Created 16 purchases and 4 sales")

# Create pay-per-visit transactions
def create_paypervisit_transactions():
    print("Creating pay-per-visit transactions...")

    # Get cashiers
    cashiers = User.objects.filter(role__in=['cashier', 'employee'])
    if not cashiers:
        print("No cashiers or employees found. Please create users first.")
        return

    # Create or get pay-per-visit settings
    settings, created = PayPerVisitSettings.objects.get_or_create(
        defaults={
            'price_per_person': 4000,
            'quick_select_1': 2,
            'quick_select_2': 5,
            'quick_select_3': 10,
            'price_for_2': 8000,
            'price_for_5': 20000,
            'price_for_10': 40000,
            'custom_price_1': 8000,
            'custom_price_2': 20000,
            'custom_price_3': 40000
        }
    )

    # Create 4 pay-per-visit transactions in the last 30 days
    for i in range(1, 5):
        # Generate transaction date
        transaction_date = timezone.now() - timedelta(days=random.randint(0, 30))

        # Select a random cashier
        cashier = random.choice(cashiers)

        # Determine number of people
        num_people = random.choice([1, 2, 2, 3, 4, 5, 5, 6, 8, 10])

        # Calculate amount
        if num_people == 2:
            amount = settings.price_for_2
        elif num_people == 5:
            amount = settings.price_for_5
        elif num_people == 10:
            amount = settings.price_for_10
        else:
            amount = settings.price_per_person * num_people

        # Create the transaction
        transaction = PayPerVisit.objects.create(
            trxId=generate_transaction_id("PPV"),
            amount=amount,
            num_people=num_people,
            date=transaction_date,
            received_by=cashier
        )

        # Update funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds += amount
            meta.save()

        print(f"Created pay-per-visit transaction {transaction.trxId} for {num_people} people, amount: {amount}៛")

    print(f"Created 4 pay-per-visit transactions")

# Create member payments
def create_member_payments():
    print("Creating member payments...")

    # Get members
    members = Member.objects.all()
    if not members:
        print("No members found. Please create members first.")
        return

    # Get cashiers
    cashiers = User.objects.filter(role__in=['cashier', 'employee'])
    if not cashiers:
        print("No cashiers or employees found. Please create users first.")
        return

    # Create payments for members (limit to 10 total payments)
    payment_count = 0
    max_payments = 10

    for member in members:
        # Skip if member has no package or we've reached max payments
        if not member.package or payment_count >= max_payments:
            continue

        # Number of payments to create (1 payment per member, some get 2)
        num_payments = 1 if payment_count >= 8 else random.randint(1, 2)

        for i in range(num_payments):
            # Generate payment date
            payment_date = random.choice([
                member.start_date + timedelta(days=random.randint(0, 5)),  # Payment at start
                member.end_date - timedelta(days=random.randint(5, 30))    # Payment near end
            ])

            # Convert to datetime
            payment_datetime = datetime.combine(payment_date, datetime.min.time())
            payment_datetime = pytz.timezone('Asia/Phnom_Penh').localize(payment_datetime)

            # Select a random cashier
            cashier = random.choice(cashiers)

            # Create the payment
            payment = Payment.objects.create(
                invoice_no=generate_transaction_id("INV"),
                member=member,
                amount_khr=member.package.price_khr - member.discount,
                amount_usd=member.package.price_usd,
                payment_method=random.choice(['cash', 'aba', 'wing', 'other']),
                payment_date=payment_datetime,
                collector=cashier,
                notes=f"Payment for {member.package.name} package"
            )

            # Update member payment status
            member.payment_status = 'paid'
            member.due_payment = 0
            member.save()

            # Update funds
            meta = MetaData.objects.last()
            if meta:
                meta.funds += payment.amount_khr
                meta.save()

            print(f"Created payment {payment.invoice_no} for member {member.name}, amount: {payment.amount_khr}៛")

            # Increment payment count and check if we've reached the limit
            payment_count += 1
            if payment_count >= max_payments:
                break

        # Break outer loop if we've reached max payments
        if payment_count >= max_payments:
            break

    print(f"Created {payment_count} member payments")

# Create salary payments
def create_salary_payments():
    print("Creating salary payments...")

    # Get employees
    employees = User.objects.filter(is_employee=True, salary__isnull=False)
    if not employees:
        print("No employees found. Please create users first.")
        return

    # Get admin or employee for payment
    admins = User.objects.filter(role__in=['admin', 'employee'])
    if not admins:
        print("No admins or employees found. Please create users first.")
        return

    # Create salary payments for the last 2 months (6 employees x 2 months = 12, but we'll limit to 10)
    payment_count = 0
    max_payments = 10

    for month_offset in range(2):
        # Calculate payment month (current month - offset)
        payment_month = timezone.now().replace(day=1) - timedelta(days=month_offset * 30)

        for employee in employees:
            # Skip if no salary or we've reached max payments
            if not employee.salary or payment_count >= max_payments:
                continue

            # Select a random admin
            admin = random.choice(admins)

            # Determine bonus and deduction
            bonus = random.choice([0, 0, 0, 50000, 100000])
            deduction = random.choice([0, 0, 0, 20000, 50000])

            # Calculate final pay
            final_pay = employee.salary + bonus - deduction

            # Generate unique payroll ID
            payroll_id = generate_transaction_id("PAY")

            # Create the salary payment
            payment = SalaryPayment.objects.create(
                payroll_id=payroll_id,
                employee=employee,
                month=payment_month,
                base_salary=employee.salary,
                bonus=bonus,
                deduction=deduction,
                overtime_hours=0,
                final_pay=final_pay,
                payment_method=random.choice(['cash', 'bank', 'other']),
                payment_date=payment_month.replace(day=random.randint(25, 28)),
                payment_status='paid',
                employment_type='full_time',
                notes=f"Salary payment for {payment_month.strftime('%B %Y')}"
            )

            print(f"Created salary payment for {employee.name}, amount: {payment.final_pay}៛")

            # Increment payment count and check if we've reached the limit
            payment_count += 1
            if payment_count >= max_payments:
                break

        # Break outer loop if we've reached max payments
        if payment_count >= max_payments:
            break

    print(f"Created {payment_count} salary payments")

# Create print templates
def create_print_templates():
    print("Creating print templates...")

    # Create payment receipt templates
    payment_templates = [
        {
            'name': 'Standard Receipt',
            'is_default': True,
            'language': 'en',
            'header_text': 'LEGEND FITNESS',
            'subheader_text': 'Payment Receipt',
            'footer_text': 'Thank you for your payment!',
            'background_color': '#ffffff',
            'text_color': '#000000',
            'accent_color': '#0c4a6e',
            'show_company_info': True,
            'show_signatures': True
        },
        {
            'name': 'Khmer Receipt',
            'is_default': False,
            'language': 'km',
            'header_text': 'LEGEND FITNESS',
            'subheader_text': 'បង្កាន់ដៃបង់ប្រាក់',
            'footer_text': 'សូមអរគុណសម្រាប់ការបង់ប្រាក់របស់អ្នក!',
            'background_color': '#ffffff',
            'text_color': '#000000',
            'accent_color': '#0c4a6e',
            'show_company_info': True,
            'show_signatures': True
        },
        {
            'name': 'Minimal Receipt',
            'is_default': False,
            'language': 'en',
            'header_text': 'LEGEND FITNESS',
            'subheader_text': 'Receipt',
            'footer_text': 'Thank you',
            'background_color': '#f8f9fa',
            'text_color': '#212529',
            'accent_color': '#0d6efd',
            'show_company_info': False,
            'show_signatures': False
        }
    ]

    # Create payment templates
    for template_data in payment_templates:
        PaymentTemplate.objects.get_or_create(
            name=template_data['name'],
            defaults=template_data
        )

    print(f"Created {len(payment_templates)} payment templates")

# Create finance transactions (deposits and withdrawals)
def create_finance_transactions():
    print("Creating finance transactions...")

    # Get staff members who can make transactions
    staff_members = User.objects.filter(role__in=['admin', 'employee', 'cashier'])
    if not staff_members:
        print("No staff members found. Please create users first.")
        return

    # Create deposits (income transactions)
    deposit_sources = ['membership', 'product', 'paypervisit', 'other']
    for i in range(6):  # 6 deposits in last 60 days
        transaction_date = timezone.now() - timedelta(days=random.randint(0, 60))
        staff = random.choice(staff_members)
        source = random.choice(deposit_sources)

        # Realistic deposit amounts based on source
        if source == 'membership':
            amount_khr = random.choice([120000, 300000, 550000, 1000000])  # Package prices
        elif source == 'product':
            amount_khr = random.randint(50000, 500000)
        elif source == 'paypervisit':
            amount_khr = random.randint(20000, 100000)
        else:
            amount_khr = random.randint(100000, 800000)

        amount_usd = round(amount_khr / 4000, 2)  # Approximate exchange rate

        # Create deposit transaction (transaction_id will be auto-generated by model)
        FinanceTransaction.objects.create(
            transaction_type='deposit',
            amount_khr=amount_khr,
            amount_usd=amount_usd,
            payment_method=random.choice(['cash', 'bank', 'other']),
            source=source,
            notes=f"Deposit from {source} - {transaction_date.strftime('%B %Y')}",
            status='completed',
            staff=staff,
            transaction_date=transaction_date
        )
        print(f"Created deposit: {amount_khr:,}៛ from {source}")

    # Create withdrawals (expense transactions)
    withdrawal_purposes = ['salary', 'utilities', 'maintenance', 'equipment', 'other']
    for i in range(4):  # 4 withdrawals in last 60 days
        transaction_date = timezone.now() - timedelta(days=random.randint(0, 60))
        staff = random.choice(staff_members)
        purpose = random.choice(withdrawal_purposes)

        # Realistic withdrawal amounts based on purpose
        if purpose == 'salary':
            amount_khr = random.choice([600000, 700000, 800000, 900000, 1200000, 1500000])
        elif purpose == 'utilities':
            amount_khr = random.randint(200000, 800000)
        elif purpose == 'maintenance':
            amount_khr = random.randint(100000, 500000)
        elif purpose == 'equipment':
            amount_khr = random.randint(500000, 2000000)
        else:
            amount_khr = random.randint(50000, 300000)

        amount_usd = round(amount_khr / 4000, 2)

        # Create withdrawal transaction (transaction_id will be auto-generated by model)
        FinanceTransaction.objects.create(
            transaction_type='withdrawal',
            amount_khr=amount_khr,
            amount_usd=amount_usd,
            payment_method=random.choice(['cash', 'bank', 'other']),
            purpose=purpose,
            notes=f"Withdrawal for {purpose} - {transaction_date.strftime('%B %Y')}",
            status='completed',
            staff=staff,
            approved_by=random.choice(User.objects.filter(role__in=['admin', 'employee'])),
            transaction_date=transaction_date
        )
        print(f"Created withdrawal: {amount_khr:,}៛ for {purpose}")

    print(f"Created 6 deposits and 4 withdrawals (10 total finance transactions)")

# Create bills for gym expenses
def create_bills():
    print("Creating bills...")

    # Get staff who can pay bills
    staff_members = User.objects.filter(role__in=['admin', 'employee'])
    if not staff_members:
        print("No staff members found. Please create users first.")
        return

    # Cambodian utility and service providers
    providers = {
        'electricity': ['EDC (Electricité du Cambodge)', 'Phnom Penh Power Supply', 'Cambodia Electricity'],
        'water': ['PPWSA (Phnom Penh Water Supply)', 'Cambodia Water Supply', 'Siem Reap Water Supply'],
        'internet': ['Cellcard', 'Smart Axiata', 'Metfone', 'EZECOM'],
        'rent': ['Legend Building Management', 'Phnom Penh Property', 'Cambodia Real Estate'],
        'maintenance': ['Gym Equipment Service', 'Building Maintenance Co.', 'Technical Support'],
        'equipment': ['Fitness Equipment Cambodia', 'Gym Gear Supplier', 'Sports Equipment'],
        'supplies': ['Cleaning Supplies Co.', 'Office Supplies', 'General Supplies'],
        'other': ['Security Service', 'Insurance Company', 'Legal Services']
    }

    # Create 3 bills for the current month
    bill_month = timezone.now().replace(day=1)

    # Create exactly 3 bills
    bill_categories = ['electricity', 'water', 'rent']  # Select 3 main categories
    for i, category in enumerate(bill_categories):
        provider = random.choice(providers[category])

        # Realistic amounts for each category in Cambodia
        if category == 'electricity':
            amount_khr = random.randint(800000, 1500000)  # $200-375
        elif category == 'water':
            amount_khr = random.randint(200000, 500000)   # $50-125
        elif category == 'rent':
            amount_khr = random.randint(4000000, 8000000) # $1000-2000
        else:
            amount_khr = random.randint(200000, 800000)   # $50-200

        amount_usd = round(amount_khr / 4000, 2)
        payment_status = random.choice(['paid', 'paid', 'pending'])  # Most bills are paid

        # Create bill (bill_id will be auto-generated by model)
        Bill.objects.create(
            category=category,
            provider=provider,
            description=f"{category.title()} bill for {bill_month.strftime('%B %Y')}",
            month_year=bill_month.date(),
            payment_period=1,  # Monthly
            amount_khr=amount_khr,
            amount_usd=amount_usd,
            payment_method=random.choice(['cash', 'bank', 'other']),
            payment_status=payment_status,
            paid_by=random.choice(staff_members) if payment_status == 'paid' else None,
            is_recurring=random.choice([True, False]),
            notes=f"Monthly {category} expense"
        )

        print(f"Created {category} bill: {provider} - {amount_khr:,}៛ ({payment_status})")

    print(f"Created 3 bills")

# Initialize system settings
def init_settings():
    print("Initializing system settings...")

    # Create or update settings
    settings, created = Settings.objects.get_or_create(
        id=1,
        defaults={
            'gym_name': 'Legend Fitness Club',
            'contact_email': '<EMAIL>',
            'contact_phone': '012 345 678',
            'address': '#123, Street 271, Chamkar Mon, Phnom Penh, Cambodia',
            'auto_deactivate_out_of_stock': True,
            'auto_reactivate_in_stock': True,
            'default_items_per_page': 10,
            'paypervisit_price_per_person': 4000,
            'paypervisit_quick_select_1': 2,
            'paypervisit_quick_select_2': 5,
            'paypervisit_quick_select_3': 10,
            'paypervisit_custom_price_1': 8000,
            'paypervisit_custom_price_2': 20000,
            'paypervisit_custom_price_3': 40000,
            'notification_success_color': '#065f46',
            'notification_error_color': '#b91c1c',
            'notification_warning_color': '#b45309',
            'notification_info_color': '#1e3a8a',
            'notification_text_color': '#ffffff',
            'default_currency': 'KHR',
            'exchange_rate_usd_to_khr': 4000,
            'funds': 0,
            'last_checked': timezone.now().date()
        }
    )

    if created:
        print("Created system settings")
    else:
        print("System settings already exist")

# Initialize metadata if it doesn't exist
def init_metadata():
    print("Initializing metadata...")

    if not MetaData.objects.exists():
        MetaData.objects.create(
            lastChecked=timezone.now(),
            funds=0
        )
        print("Created initial MetaData record")
    else:
        print("MetaData record already exists")

# Main function to run all data creation
def create_demo_data():
    with transaction.atomic():
        print("=== Starting Comprehensive Demo Data Creation ===")
        print("Creating realistic data for Legend Fitness Club - Cambodia")
        print()

        # Initialize system components
        init_settings()
        init_metadata()

        # Create users and employees
        create_users()

        # Create membership packages
        create_packages()

        # Create members (30 members with varied statuses)
        create_members()

        # Create products and suppliers
        create_products_and_suppliers()

        # Create purchases and sales
        create_purchases_and_sales()

        # Create pay-per-visit transactions
        create_paypervisit_transactions()

        # Create member payments
        create_member_payments()

        # Create salary payments
        create_salary_payments()

        # Create finance transactions (deposits/withdrawals)
        create_finance_transactions()

        # Create bills (utilities, rent, etc.)
        create_bills()

        # Create print templates
        create_print_templates()

        print()
        print("=== Demo Data Creation Summary ===")
        print(f"✅ Users/Employees: {User.objects.count()}")
        print(f"✅ Members: {Member.objects.count()}")
        print(f"✅ Packages: {Package.objects.count()}")
        print(f"✅ Products: {Product.objects.count()}")
        print(f"✅ Suppliers: {Supplier.objects.count()}")
        print(f"✅ Purchases: {Purchase.objects.count()}")
        print(f"✅ Sales: {Sale.objects.count()}")
        print(f"✅ Pay-per-visit: {PayPerVisit.objects.count()}")
        print(f"✅ Member Payments: {Payment.objects.count()}")
        print(f"✅ Salary Payments: {SalaryPayment.objects.count()}")
        print(f"✅ Finance Transactions: {FinanceTransaction.objects.count()}")
        print(f"✅ Bills: {Bill.objects.count()}")
        print()
        print("👥 Employee Breakdown:")
        print("   - 1 Coach, 2 Cashiers, 1 Cleaner, 1 Security Guard")
        print()
        print("🎉 Comprehensive demo data creation completed successfully!")
        print("📊 The system now contains focused, realistic data for a Cambodian gym management system")
        print("🔗 You can now access the system at: http://127.0.0.1:8000/")

if __name__ == '__main__':
    create_demo_data()
